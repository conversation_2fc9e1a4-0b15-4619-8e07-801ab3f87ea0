const fs = require('fs');
const axios = require('axios');

class RAGSystem {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.chunks = [];
        this.embeddings = [];
        this.embeddingApiUrl = `https://generativelanguage.googleapis.com/v1beta/models/text-embedding-004:embedContent?key=${apiKey}`;
    }

    // Bước 1: Chia dữ liệu thành chunks
    createChunks(data, chunkSize = 400) {
        const chunks = [];
        let chunkId = 1;

        for (const [key, content] of Object.entries(data)) {
            // Làm sạch nội dung
            const cleanContent = this.cleanText(content);
            
            // Chia thành các đoạn nhỏ
            const sentences = cleanContent.split(/[.!?]+/).filter(s => s.trim().length > 0);
            let currentChunk = '';
            
            for (const sentence of sentences) {
                const trimmedSentence = sentence.trim();
                if (!trimmedSentence) continue;

                // Nếu thêm câu này vào chunk hiện tại mà vượt quá kích thước
                if (currentChunk.length + trimmedSentence.length > chunkSize && currentChunk.length > 0) {
                    chunks.push({
                        id: chunkId++,
                        content: currentChunk.trim(),
                        source: key
                    });
                    currentChunk = trimmedSentence;
                } else {
                    currentChunk += (currentChunk ? '. ' : '') + trimmedSentence;
                }
            }

            // Thêm chunk cuối cùng nếu có
            if (currentChunk.trim()) {
                chunks.push({
                    id: chunkId++,
                    content: currentChunk.trim(),
                    source: key
                });
            }
        }

        this.chunks = chunks;
        console.log(`✅ Đã tạo ${chunks.length} chunks từ dữ liệu`);
        return chunks;
    }

    // Làm sạch text
    cleanText(text) {
        return text
            .replace(/\s+/g, ' ')
            .replace(/[^\w\sÀ-ỹ.,!?()-]/g, '')
            .replace(/\b(function|var|const|let|document|window|console)\b.*$/gm, '')
            .replace(/\{[^}]*\}/g, '')
            .replace(/\([^)]*\)/g, '')
            .trim();
    }

    // Bước 2: Tạo embeddings cho chunks
    async createEmbeddings() {
        console.log('🔄 Đang tạo embeddings...');
        const embeddings = [];

        for (let i = 0; i < this.chunks.length; i++) {
            const chunk = this.chunks[i];
            try {
                const embedding = await this.getEmbedding(chunk.content);
                embeddings.push({
                    id: chunk.id,
                    embedding: embedding,
                    content: chunk.content,
                    source: chunk.source
                });
                
                if ((i + 1) % 10 === 0) {
                    console.log(`📊 Đã xử lý ${i + 1}/${this.chunks.length} chunks`);
                }
                
                // Delay để tránh rate limit
                await this.delay(100);
            } catch (error) {
                console.error(`❌ Lỗi tạo embedding cho chunk ${chunk.id}:`, error.message);
            }
        }

        this.embeddings = embeddings;
        console.log(`✅ Đã tạo ${embeddings.length} embeddings`);
        return embeddings;
    }

    // Gọi API để tạo embedding
    async getEmbedding(text) {
        const response = await axios.post(this.embeddingApiUrl, {
            model: "models/text-embedding-004",
            content: {
                parts: [{ text: text }]
            }
        }, {
            headers: { 'Content-Type': 'application/json' }
        });

        return response.data.embedding.values;
    }

    // Bước 3: Tìm kiếm chunks liên quan
    async searchRelevantChunks(query, topK = 5) {
        // Tạo embedding cho câu hỏi
        const queryEmbedding = await this.getEmbedding(query);
        
        // Tính độ tương đồng cosine
        const similarities = this.embeddings.map(item => ({
            ...item,
            similarity: this.cosineSimilarity(queryEmbedding, item.embedding)
        }));

        // Sắp xếp theo độ tương đồng và lấy top K
        const topChunks = similarities
            .sort((a, b) => b.similarity - a.similarity)
            .slice(0, topK);

        console.log(`🔍 Tìm thấy ${topChunks.length} chunks liên quan cho: "${query}"`);
        topChunks.forEach((chunk, index) => {
            console.log(`${index + 1}. Score: ${chunk.similarity.toFixed(3)} - Source: ${chunk.source}`);
        });

        return topChunks;
    }

    // Tính độ tương đồng cosine
    cosineSimilarity(vecA, vecB) {
        const dotProduct = vecA.reduce((sum, a, i) => sum + a * vecB[i], 0);
        const magnitudeA = Math.sqrt(vecA.reduce((sum, a) => sum + a * a, 0));
        const magnitudeB = Math.sqrt(vecB.reduce((sum, b) => sum + b * b, 0));
        return dotProduct / (magnitudeA * magnitudeB);
    }

    // Lưu embeddings vào file
    saveEmbeddings(filename = 'embeddings.json') {
        const data = {
            chunks: this.chunks,
            embeddings: this.embeddings,
            timestamp: new Date().toISOString()
        };
        fs.writeFileSync(filename, JSON.stringify(data, null, 2));
        console.log(`💾 Đã lưu embeddings vào ${filename}`);
    }

    // Load embeddings từ file
    loadEmbeddings(filename = 'embeddings.json') {
        try {
            const data = JSON.parse(fs.readFileSync(filename, 'utf8'));
            this.chunks = data.chunks;
            this.embeddings = data.embeddings;
            console.log(`📂 Đã load ${this.embeddings.length} embeddings từ ${filename}`);
            return true;
        } catch (error) {
            console.log(`⚠️ Không thể load embeddings từ ${filename}:`, error.message);
            return false;
        }
    }

    // Utility function
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Bước 4: Tạo context cho AI từ chunks đã lọc
    createContextFromChunks(relevantChunks) {
        return relevantChunks
            .map(chunk => `[${chunk.source}] ${chunk.content}`)
            .join('\n\n');
    }
}

module.exports = RAGSystem;
