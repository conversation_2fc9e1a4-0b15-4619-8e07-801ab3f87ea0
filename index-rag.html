<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Technojet AI Assistant - RAG Version</title>
    <!-- Link font google -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@24,400,0,0" />
    <style>
        /* Import Google Fonts-Inter */
        @import url('https://fonts.googleapis.com/css2?family=Inter:opsz,wght@14..32,100..900&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Inter", sans-serif;
        }

        body {
            min-height: 100vh;
            background: linear-gradient(#27613b, #ce9f37);
        }

        #chatbot-toggler {
            position: fixed;
            bottom: 30px;
            right: 35px;
            border: none;
            height: 50px;
            width: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 50%;
            background: #27613b;
            transition: all 0.2s ease;
        }

        body.show-chatbot #chatbot-toggler {
            transform: rotate(90deg);
        }

        #chatbot-toggler span {
            color: #fff;
            position: absolute;
        }

        body.show-chatbot #chatbot-toggler span:first-child,
        #chatbot-toggler span:last-child {
            opacity: 0;
        }

        body.show-chatbot #chatbot-toggler span:last-child {
            opacity: 1;
        }

        .chatbot-popup {
            position: fixed;
            right: 35px;
            bottom: 90px;
            width: 420px;
            background: #fff;
            overflow: hidden;
            border-radius: 15px;
            opacity: 0;
            transform: scale(0.2);
            transform-origin: bottom right;
            pointer-events: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            transition: all 0.1s ease;
        }

        body.show-chatbot .chatbot-popup {
            opacity: 1;
            pointer-events: auto;
            transform: scale(1);
        }

        .chat-header {
            display: flex;
            align-items: center;
            background: #27613b;
            padding: 15px 22px;
            justify-content: space-between;
        }

        .chat-header .header-infor {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .header-infor .chatbot-logo {
            height: 35px;
            width: 35px;
            padding: 6px;
            fill: #27613b;
            flex-shrink: 0;
            background: #fff;
            border-radius: 50%;
        }

        .header-infor .logo-text {
            color: #fff;
            font-size: 1.31rem;
            font-weight: 600;
        }

        .rag-indicator {
            background: #a1df99;
            color: #27613b;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 600;
            margin-left: 10px;
        }

        .chat-header #close-chatbot {
            border: none;
            color: #fff;
            height: 40px;
            width: 40px;
            font-size: 1.9rem;
            margin-right: -10px;
            padding-top: 2px;
            cursor: pointer;
            border-radius: 50%;
            background: none;
            transition: 0.2s ease;
        }

        .chat-header #close-chatbot:hover {
            background: #a1df99;
        }

        .chat-body {
            padding: 25px 22px;
            margin-bottom: 82px;
            display: flex;
            gap: 20px;
            height: 460px;
            overflow-y: auto;
            flex-direction: column;
            scrollbar-width: thin;
            scrollbar-color: #27613b transparent;
        }

        .chat-body .message {
            display: flex;
            gap: 11px;
            align-items: center;
        }

        .chat-body .bot-message .bot-avatar {
            height: 35px;
            width: 35px;
            padding: 6px;
            fill: #fff;
            flex-shrink: 0;
            margin-bottom: 2px;
            align-self: flex-end;
            background: #27613b;
            border-radius: 50%;
        }

        .chat-body .user-message {
            flex-direction: column;
            align-items: flex-end;
        }

        .chat-body .message .message-text {
            padding: 12px 16px;
            max-width: 75%;
            font-size: 0.95rem;
        }

        .chat-body .bot-message.thinking .message-text {
            padding: 2px 16px;
        }

        .chat-body .bot-message .message-text {
            background: #a1df99;
            border-radius: 13px 13px 13px 3px;
        }

        .chat-body .user-message .message-text {
            color: #fff;
            background: #27613b;
            border-radius: 13px 13px 3px 13px;
        }

        .chat-body .bot-message .thinking-indicator {
            display: flex;
            gap: 4px;
            padding-block: 15px;
        }

        .chat-body .bot-message .thinking-indicator .dot {
            height: 7px;
            width: 7px;
            opacity: 0.7;
            border-radius: 50%;
            background: #27613b;
            animation: dotPulse 1.8s ease-in-out infinite;
        }

        .chat-body .bot-message .thinking-indicator .dot:nth-child(1) {
            animation-delay: 0.2s;
        }

        .chat-body .bot-message .thinking-indicator .dot:nth-child(2) {
            animation-delay: 0.3s;
        }

        .chat-body .bot-message .thinking-indicator .dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes dotPulse {
            0%, 44% {
                transform: translateY(0);
            }
            28% {
                opacity: 0.4;
                transform: translateY(-4px);
            }
            44% {
                opacity: 0.2;
            }
        }

        .chat-footer {
            position: absolute;
            bottom: 0;
            width: 100%;
            background: #fff;
            padding: 15px 22px 20px;
        }

        .chat-footer .chat-form {
            display: flex;
            position: relative;
            align-items: center;
            background: #fff;
            border-radius: 32px;
            outline: 1px solid #cccce5;
        }

        .chat-footer .chat-form:focus-within {
            outline: 2px solid #5350c4;
        }

        .chat-form .message-input {
            border: none;
            outline: none;
            height: 47px;
            width: 100%;
            resize: none;
            max-height: 180px;
            white-space: pre-line;
            font-size: 0.95rem;
            padding: 14px 0 13px 18px;
            border-radius: inherit;
            scrollbar-width: thin;
            scrollbar-color: transparent transparent;
        }

        .chat-form .message-input:hover {
            scrollbar-color: #27613b transparent;
        }

        .chat-form .chat-controls {
            display: flex;
            height: 47px;
            gap: 3px;
            align-items: center;
            align-self: flex-end;
            padding-right: 6px;
        }

        .chat-form .chat-controls button {
            height: 35px;
            width: 35px;
            border: none;
            font-size: 1.15rem;
            cursor: pointer;
            color: #3f9934;
            background: none;
            border-radius: 50%;
            transition: 0.2s ease;
        }

        .chat-form .chat-controls #send-message {
            color: #fff;
            display: none;
            background: #27613b;
        }

        .chat-form .message-input:valid~.chat-controls #send-message {
            display: block;
        }

        .chat-form .chat-controls #send-message:hover {
            background: #3f9934;
        }

        .chat-form .chat-controls button:hover {
            background: #f1f1ff;
        }

        .rag-stats {
            font-size: 0.7rem;
            color: #666;
            margin-top: 5px;
            padding: 5px 10px;
            background: #f0f0f0;
            border-radius: 8px;
        }

        @media (max-width:520px) {
            #chatbot-toggler {
                right: 20px;
                bottom: 20px;
            }

            .chatbot-popup {
                right: 0;
                bottom: 0;
                height: 100%;
                border-radius: 0;
                width: 100%;
            }

            .chatbot-popup .chat-header {
                padding: 12px 15px;
            }

            .chat-body {
                height: calc(90% - 55px);
                padding: 25px 15px;
            }

            .chat-footer {
                padding: 10px 15px 15px;
            }
        }
    </style>
</head>

<body>
    <button id="chatbot-toggler">
        <span class="material-symbols-rounded">mode_comment</span>
        <span class="material-symbols-rounded">close</span>
    </button>
    <div class="chatbot-popup">
        <!-- Chatbot header -->
        <div class="chat-header">
            <div class="header-infor">
                <svg class="chatbot-logo" xmlns="http://www.w3.org/2000/svg" width="50" height="50"
                    viewBox="0 0 1024 1024">
                    <path
                        d="M738.3 287.6H285.7c-59 0-106.8 47.8-106.8 106.8v303.1c0 59 47.8 106.8 106.8 106.8h81.5v111.1c0 .7.8 1.1 1.4.7l166.9-110.6 41.8-.8h117.4l43.6-.4c59 0 106.8-47.8 106.8-106.8V394.5c0-59-47.8-106.9-106.8-106.9zM351.7 448.2c0-29.5 23.9-53.5 53.5-53.5s53.5 23.9 53.5 53.5-23.9 53.5-53.5 53.5-53.5-23.9-53.5-53.5zm157.9 267.1c-67.8 0-123.8-47.5-132.3-109h264.6c-8.6 61.5-64.5 109-132.3 109zm110-213.7c-29.5 0-53.5-23.9-53.5-53.5s23.9-53.5 53.5-53.5 53.5 23.9 53.5 53.5-23.9 53.5-53.5 53.5zM867.2 644.5V453.1h26.5c19.4 0 35.1 15.7 35.1 35.1v121.1c0 19.4-15.7 35.1-35.1 35.1h-26.5zM95.2 609.4V488.2c0-19.4 15.7-35.1 35.1-35.1h26.5v191.3h-26.5c-19.4 0-35.1-15.7-35.1-35.1zM561.5 149.6c0 23.4-15.6 43.3-36.9 49.7v44.9h-30v-44.9c-21.4-6.5-36.9-26.3-36.9-49.7 0-28.6 23.3-51.9 51.9-51.9s51.9 23.3 51.9 51.9z">
                    </path>
                </svg>
                <h2 class="logo-text">Technojet AI</h2>
                <span class="rag-indicator">RAG</span>
            </div>
            <button id="close-chatbot" class="material-symbols-rounded">
                keyboard_arrow_down
            </button>
        </div>
        <!-- Chatbot body -->
        <div class="chat-body">
            <div class="message bot-message">
                <svg class="bot-avatar" xmlns="http://www.w3.org/2000/svg" width="50" height="50"
                    viewBox="0 0 1024 1024">
                    <path
                        d="M738.3 287.6H285.7c-59 0-106.8 47.8-106.8 106.8v303.1c0 59 47.8 106.8 106.8 106.8h81.5v111.1c0 .7.8 1.1 1.4.7l166.9-110.6 41.8-.8h117.4l43.6-.4c59 0 106.8-47.8 106.8-106.8V394.5c0-59-47.8-106.9-106.8-106.9zM351.7 448.2c0-29.5 23.9-53.5 53.5-53.5s53.5 23.9 53.5 53.5-23.9 53.5-53.5 53.5-53.5-23.9-53.5-53.5zm157.9 267.1c-67.8 0-123.8-47.5-132.3-109h264.6c-8.6 61.5-64.5 109-132.3 109zm110-213.7c-29.5 0-53.5-23.9-53.5-53.5s23.9-53.5 53.5-53.5 53.5 23.9 53.5 53.5-23.9 53.5-53.5 53.5zM867.2 644.5V453.1h26.5c19.4 0 35.1 15.7 35.1 35.1v121.1c0 19.4-15.7 35.1-35.1 35.1h-26.5zM95.2 609.4V488.2c0-19.4 15.7-35.1 35.1-35.1h26.5v191.3h-26.5c-19.4 0-35.1-15.7-35.1-35.1zM561.5 149.6c0 23.4-15.6 43.3-36.9 49.7v44.9h-30v-44.9c-21.4-6.5-36.9-26.3-36.9-49.7 0-28.6 23.3-51.9 51.9-51.9s51.9 23.3 51.9 51.9z">
                    </path>
                </svg>
                <div class="message-text">
                    Xin chào! Tôi là trợ lý AI của Technojet với công nghệ RAG. 
                    <br>Tôi có thể giúp gì cho bạn?
                    <div class="rag-stats">🚀 Sử dụng RAG để tối ưu token</div>
                </div>
            </div>
        </div>
        <!-- Chatbot footer -->
        <div class="chat-footer">
            <form action="#" class="chat-form">
                <textarea placeholder="Message..." class="message-input" required></textarea>
                <div class="chat-controls">
                    <button type="submit" id="send-message" class="material-symbols-rounded">
                        arrow_upward
                    </button>
                </div>
            </form>
        </div>
    </div>
    <script>
        const chatBody = document.querySelector(".chat-body");
        const messageInput = document.querySelector(".message-input");
        const sendMessageButton = document.querySelector("#send-message");
        const chatbotToggler = document.querySelector("#chatbot-toggler");
        const closeChatbot = document.querySelector("#close-chatbot");

        const chatHistory = [];
        const initialInputHeight = messageInput.scrollHeight;

        const createMessageElement = (content, ...classes) => {
            const div = document.createElement("div");
            div.classList.add("message", ...classes);
            div.innerHTML = content;
            return div;
        }

        const generateBotResponse = async (incomingMessageDiv, userQuestion) => {
            const messageElement = incomingMessageDiv.querySelector(".message-text");

            try {
                console.log('Sending message:', userQuestion);

                // Gọi API RAG để chat
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: userQuestion,
                        chatHistory: chatHistory
                    })
                });

                const data = await response.json();
                console.log('Response:', data);

                if (!response.ok) {
                    throw new Error(data.error || 'Lỗi từ server');
                }

                // Hiển thị phản hồi
                messageElement.innerHTML = `
                    ${data.response}
                    <div class="rag-stats">
                        📊 Chunks: ${data.relevantChunks} |
                        💾 ${data.tokensSaved}
                    </div>
                `;

                // Lưu vào chat history
                chatHistory.push(
                    { role: "user", parts: [{ text: userQuestion }] },
                    { role: "model", parts: [{ text: data.response }] }
                );

            } catch (error) {
                console.error('Error:', error);
                messageElement.innerHTML = `
                    ❌ ${error.message}
                    <div class="rag-stats">Vui lòng thử lại sau</div>
                `;
                messageElement.style.color = "#ff0000";
            } finally {
                incomingMessageDiv.classList.remove("thinking");
                chatBody.scrollTo({ top: chatBody.scrollHeight, behavior: "smooth" });
            }
        };

        const handleOutgoingMessage = (e) => {
            e.preventDefault();
            const userMessage = messageInput.value.trim();
            if (!userMessage) return;

            console.log('User message:', userMessage);

            // Hiển thị tin nhắn user
            const messageContent = `<div class="message-text">${userMessage}</div>`;
            const outgoingMessageDiv = createMessageElement(messageContent, "user-message");
            chatBody.appendChild(outgoingMessageDiv);

            // Lưu message trước khi clear input
            const savedMessage = userMessage;
            messageInput.value = "";
            messageInput.dispatchEvent(new Event("input"));
            chatBody.scrollTo({ top: chatBody.scrollHeight, behavior: "smooth" });

            // Hiển thị bot thinking
            setTimeout(() => {
                const messageContent = `
                    <svg class="bot-avatar" xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 1024 1024">
                        <path d="M738.3 287.6H285.7c-59 0-106.8 47.8-106.8 106.8v303.1c0 59 47.8 106.8 106.8 106.8h81.5v111.1c0 .7.8 1.1 1.4.7l166.9-110.6 41.8-.8h117.4l43.6-.4c59 0 106.8-47.8 106.8-106.8V394.5c0-59-47.8-106.9-106.8-106.9zM351.7 448.2c0-29.5 23.9-53.5 53.5-53.5s53.5 23.9 53.5 53.5-23.9 53.5-53.5 53.5-53.5-23.9-53.5-53.5zm157.9 267.1c-67.8 0-123.8-47.5-132.3-109h264.6c-8.6 61.5-64.5 109-132.3 109zm110-213.7c-29.5 0-53.5-23.9-53.5-53.5s23.9-53.5 53.5-53.5 53.5 23.9 53.5 53.5-23.9 53.5-53.5 53.5zM867.2 644.5V453.1h26.5c19.4 0 35.1 15.7 35.1 35.1v121.1c0 19.4-15.7 35.1-35.1 35.1h-26.5zM95.2 609.4V488.2c0-19.4 15.7-35.1 35.1-35.1h26.5v191.3h-26.5c-19.4 0-35.1-15.7-35.1-35.1zM561.5 149.6c0 23.4-15.6 43.3-36.9 49.7v44.9h-30v-44.9c-21.4-6.5-36.9-26.3-36.9-49.7 0-28.6 23.3-51.9 51.9-51.9s51.9 23.3 51.9 51.9z"></path>
                    </svg>
                    <div class="message-text">
                        <div class="thinking-indicator">
                            <div class="dot"></div>
                            <div class="dot"></div>
                            <div class="dot"></div>
                        </div>
                    </div>
                `;
                const incomingMessageDiv = createMessageElement(messageContent, "bot-message", "thinking");
                chatBody.appendChild(incomingMessageDiv);
                chatBody.scrollTo({ top: chatBody.scrollHeight, behavior: "smooth" });
                generateBotResponse(incomingMessageDiv, savedMessage);
            }, 600);
        }

        // Event listeners
        messageInput.addEventListener("keydown", (e) => {
            const userMessage = e.target.value.trim();
            if (e.key === "Enter" && userMessage && !e.shiftKey && window.innerWidth > 768) {
                handleOutgoingMessage(e);
            }
        });

        messageInput.addEventListener("input", () => {
            messageInput.style.height = `${initialInputHeight}px`;
            messageInput.style.height = `${messageInput.scrollHeight}px`;
            document.querySelector(".chat-form").style.borderRadius =
                messageInput.scrollHeight > initialInputHeight ? "15px" : "32px";
        });

        sendMessageButton.addEventListener("click", (e) => handleOutgoingMessage(e));
        chatbotToggler.addEventListener("click", () => document.body.classList.toggle("show-chatbot"));
        closeChatbot.addEventListener("click", () => document.body.classList.remove("show-chatbot"));

        // Load stats khi khởi động
        fetch('/api/stats')
            .then(res => res.json())
            .then(stats => {
                console.log('📊 RAG Stats:', stats);
            })
            .catch(err => console.log('⚠️ Không thể load stats:', err.message));
    </script>
</body>

</html>
