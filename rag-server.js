const express = require('express');
const cors = require('cors');
const path = require('path');
const RAGSystem = require('./rag-system');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// Khởi tạo RAG system
const API_KEY = "AIzaSyABDdKPrUAlOzIv7fAN3e1axEdgFUj9Ks4";
const ragSystem = new RAGSystem(API_KEY);

// Load embeddings khi khởi động server
async function initializeRAG() {
    console.log('🔄 Khởi tạo RAG system...');
    
    // Thử load embeddings từ file
    const loaded = ragSystem.loadEmbeddings('embeddings.json');
    
    if (!loaded) {
        console.log('⚠️ Chưa có embeddings. Vui lòng chạy: node setup-rag.js');
        process.exit(1);
    }
    
    console.log('✅ RAG system đã sẵn sàng!');
}

// API endpoint để tìm kiếm chunks liên quan
app.post('/api/search', async (req, res) => {
    try {
        const { query, topK = 5 } = req.body;
        
        if (!query) {
            return res.status(400).json({ error: 'Query is required' });
        }
        
        console.log(`🔍 Tìm kiếm: "${query}"`);
        
        // Tìm chunks liên quan
        const relevantChunks = await ragSystem.searchRelevantChunks(query, topK);
        
        // Tạo context từ chunks
        const context = ragSystem.createContextFromChunks(relevantChunks);
        
        res.json({
            success: true,
            query: query,
            relevantChunks: relevantChunks.map(chunk => ({
                id: chunk.id,
                content: chunk.content,
                source: chunk.source,
                similarity: chunk.similarity
            })),
            context: context,
            tokensSaved: `Giảm từ ${JSON.stringify(ragSystem.chunks).length} xuống ${context.length} ký tự`
        });
        
    } catch (error) {
        console.error('❌ Lỗi tìm kiếm:', error);
        res.status(500).json({ error: error.message });
    }
});

// API endpoint để chat với RAG
app.post('/api/chat', async (req, res) => {
    try {
        const { message, chatHistory = [] } = req.body;
        
        if (!message) {
            return res.status(400).json({ error: 'Message is required' });
        }
        
        console.log(`💬 Chat: "${message}"`);
        
        // Tìm chunks liên quan
        const relevantChunks = await ragSystem.searchRelevantChunks(message, 5);
        const context = ragSystem.createContextFromChunks(relevantChunks);
        
        // Tạo prompt cho AI với context đã lọc
        const prompt = `Bạn là trợ lý AI của Technojet. Chỉ trả lời ngắn gọn xúc tích nhưng thân thiện và không cứng nhắc các câu hỏi liên quan đến Technojet dựa trên cơ sở dữ liệu sau. 
Câu trả lời tránh dùng các ký tự đặt biệt như * chỉ dùng "." và ",". 

Dữ liệu liên quan của Technojet:
${context}

Câu hỏi của người dùng: ${message}`;

        // Gọi Gemini API
        const geminiResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=${API_KEY}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                contents: [
                    ...chatHistory,
                    {
                        role: "user",
                        parts: [{ text: prompt }]
                    }
                ]
            })
        });
        
        const geminiData = await geminiResponse.json();
        
        if (!geminiResponse.ok) {
            throw new Error(geminiData.error?.message || 'Lỗi từ Gemini API');
        }
        
        const aiResponse = geminiData.candidates[0].content.parts[0].text.trim();
        
        res.json({
            success: true,
            response: aiResponse,
            relevantChunks: relevantChunks.length,
            contextLength: context.length,
            originalDataLength: JSON.stringify(ragSystem.chunks).length,
            tokensSaved: `Giảm ${Math.round((1 - context.length / JSON.stringify(ragSystem.chunks).length) * 100)}% tokens`
        });
        
    } catch (error) {
        console.error('❌ Lỗi chat:', error);
        res.status(500).json({ error: error.message });
    }
});

// Serve index.html
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index-rag.html'));
});

// API để lấy thống kê
app.get('/api/stats', (req, res) => {
    res.json({
        totalChunks: ragSystem.chunks.length,
        totalEmbeddings: ragSystem.embeddings.length,
        avgChunkLength: ragSystem.chunks.reduce((sum, chunk) => sum + chunk.content.length, 0) / ragSystem.chunks.length,
        sources: [...new Set(ragSystem.chunks.map(chunk => chunk.source))]
    });
});

// Khởi động server
initializeRAG().then(() => {
    app.listen(PORT, () => {
        console.log(`🚀 RAG Server đang chạy tại http://localhost:${PORT}`);
        console.log(`📊 API endpoints:`);
        console.log(`   - POST /api/search - Tìm kiếm chunks`);
        console.log(`   - POST /api/chat - Chat với RAG`);
        console.log(`   - GET /api/stats - Thống kê hệ thống`);
    });
});

module.exports = app;
