const fs = require('fs');
const RAGSystem = require('./rag-system');

// Script để setup hệ thống RAG
async function setupRAG() {
    console.log('🚀 Bắt đầu setup hệ thống RAG...');
    
    // Đọc API key từ environment hoặc hardcode (nên dùng .env)
    const API_KEY = "AIzaSyABDdKPrUAlOzIv7fAN3e1axEdgFUj9Ks4"; // Thay bằng API key của bạn
    
    // Khởi tạo RAG system
    const ragSystem = new RAGSystem(API_KEY);
    
    try {
        // Đọc dữ liệu từ data.json
        console.log('📖 Đọc dữ liệu từ data.json...');
        const rawData = JSON.parse(fs.readFileSync('data.json', 'utf8'));
        
        // Bước 1: Tạo chunks
        console.log('✂️ Chia dữ liệu thành chunks...');
        ragSystem.createChunks(rawData, 400); // 400 ký tự mỗi chunk
        
        // Bước 2: Tạo embeddings
        console.log('🧠 Tạo embeddings cho các chunks...');
        await ragSystem.createEmbeddings();
        
        // Bước 3: Lưu embeddings
        console.log('💾 Lưu embeddings...');
        ragSystem.saveEmbeddings('embeddings.json');
        
        console.log('✅ Setup RAG hoàn tất!');
        console.log(`📊 Thống kê:`);
        console.log(`   - Số chunks: ${ragSystem.chunks.length}`);
        console.log(`   - Số embeddings: ${ragSystem.embeddings.length}`);
        
        // Test thử
        console.log('\n🧪 Test tìm kiếm...');
        const testQuery = "xe tra nạp nhiên liệu";
        const relevantChunks = await ragSystem.searchRelevantChunks(testQuery, 3);
        
        console.log('\n📋 Kết quả test:');
        relevantChunks.forEach((chunk, index) => {
            console.log(`${index + 1}. [${chunk.source}] ${chunk.content.substring(0, 100)}...`);
            console.log(`   Similarity: ${chunk.similarity.toFixed(3)}\n`);
        });
        
    } catch (error) {
        console.error('❌ Lỗi setup RAG:', error);
    }
}

// Chạy setup
if (require.main === module) {
    setupRAG();
}

module.exports = setupRAG;
