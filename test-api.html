<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test RAG API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #27613b;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3f9934;
        }
        .result {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            white-space: pre-wrap;
        }
        input[type="text"] {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test RAG API</h1>
        
        <div class="test-section">
            <h3>📊 Stats API (GET)</h3>
            <button onclick="testStats()">Test /api/stats</button>
            <div id="stats-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔍 Search API (POST)</h3>
            <input type="text" id="search-query" placeholder="Nhập từ khóa tìm kiếm" value="xe tra nạp nhiên liệu">
            <button onclick="testSearch()">Test /api/search</button>
            <div id="search-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>💬 Chat API (POST)</h3>
            <input type="text" id="chat-message" placeholder="Nhập câu hỏi" value="Technojet làm gì?">
            <button onclick="testChat()">Test /api/chat</button>
            <div id="chat-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🚀 Quick Tests</h3>
            <button onclick="runAllTests()">Chạy tất cả tests</button>
            <button onclick="clearResults()">Xóa kết quả</button>
        </div>
    </div>

    <script>
        async function testStats() {
            const resultDiv = document.getElementById('stats-result');
            resultDiv.textContent = 'Loading...';
            
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
            }
        }

        async function testSearch() {
            const query = document.getElementById('search-query').value;
            const resultDiv = document.getElementById('search-result');
            resultDiv.textContent = 'Loading...';
            
            try {
                const response = await fetch('/api/search', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ query: query, topK: 3 })
                });
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
            }
        }

        async function testChat() {
            const message = document.getElementById('chat-message').value;
            const resultDiv = document.getElementById('chat-result');
            resultDiv.textContent = 'Loading...';
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: message })
                });
                const data = await response.json();
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
            }
        }

        async function runAllTests() {
            console.log('🧪 Running all tests...');
            await testStats();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testSearch();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testChat();
            console.log('✅ All tests completed');
        }

        function clearResults() {
            document.getElementById('stats-result').textContent = '';
            document.getElementById('search-result').textContent = '';
            document.getElementById('chat-result').textContent = '';
        }

        // Auto test on load
        window.onload = function() {
            console.log('🚀 Test page loaded');
            testStats();
        }
    </script>
</body>
</html>
